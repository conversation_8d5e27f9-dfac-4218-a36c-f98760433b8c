"use client"

import DriverNavigationB<PERSON> from "@/components/DriverNavigationBar"
import { LinearGradient } from "expo-linear-gradient"
import { useRouter } from "expo-router"
import { useEffect, useState } from "react"
import {
  Alert,
  Dimensions,
  FlatList,
  Linking,
  Platform,
  StatusBar,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native"
import MapView, { Mark<PERSON>, PROVIDER_GOOGLE } from "react-native-maps"

const { width, height } = Dimensions.get("window")

// Student login info
const STUDENT_LOGIN = {
  name: "أحمد محمد",
  studentId: "ST-2024-001",
  university: "جامعة محمد الخامس",
}

// Student data with locations, phone numbers, and statuses
const STUDENTS = [
  {
    id: 1,
    name: "AHMAD",
    avatar: "A",
    phone: "+212600123456",
    latitude: 34.0215,
    longitude: -6.842,
    distanceFromCenter: 2.3,
    status: "waiting",
    statusText: "في الانتظار",
    statusColor: "#f59e0b", // Amber
    location: "محطة الحافلة الرئيسية",
    estimatedTime: "5 دقائق",
  },
  {
    id: 2,
    name: "ALI",
    avatar: "A",
    phone: "+212600123457",
    latitude: 34.023,
    longitude: -6.838,
    distanceFromCenter: 1.8,
    status: "onboard",
    statusText: "في الحافلة",
    statusColor: "#10b981", // Emerald
    location: "في الطريق",
    estimatedTime: "15 دقيقة",
  },
  {
    id: 3,
    name: "SARA",
    avatar: "S",
    phone: "+212600123458",
    latitude: 34.0195,
    longitude: -6.845,
    distanceFromCenter: 3.1,
    status: "late",
    statusText: "متأخر",
    statusColor: "#ef4444", // Red
    location: "لم يصل بعد",
    estimatedTime: "10 دقائق",
  },
  {
    id: 4,
    name: "OMAR",
    avatar: "O",
    phone: "+212600123459",
    latitude: 34.028,
    longitude: -6.832,
    distanceFromCenter: 1.2,
    status: "waiting",
    statusText: "في الانتظار",
    statusColor: "#f59e0b",
    location: "محطة الجامعة",
    estimatedTime: "3 دقائق",
  },
  {
    id: 5,
    name: "FATIMA",
    avatar: "F",
    phone: "+212600123460",
    latitude: 34.024,
    longitude: -6.836,
    distanceFromCenter: 1.5,
    status: "onboard",
    statusText: "في الحافلة",
    statusColor: "#10b981",
    location: "في الطريق",
    estimatedTime: "12 دقيقة",
  },
  {
    id: 6,
    name: "HASSAN",
    avatar: "H",
    phone: "+212600123461",
    latitude: 34.026,
    longitude: -6.834,
    distanceFromCenter: 0.9,
    status: "onboard",
    statusText: "في الحافلة",
    statusColor: "#10b981",
    location: "في الطريق",
    estimatedTime: "8 دقائق",
  },
]

// Central location (driver's current position)
const CENTRAL_LOCATION = {
  latitude: 34.0255,
  longitude: -6.83,
}

// Original countdown reference (30 minutes)
const ORIGINAL_COUNTDOWN = "30:00"

// Trip route data
const TRIP_ROUTE = [
  { latitude: 34.0209, longitude: -6.8416, title: "نقطة البداية" },
  { latitude: 34.025, longitude: -6.835, title: "محطة وسطية" },
  { latitude: 34.03, longitude: -6.82, title: "جامعة العلوم" },
]

export default function DriverLiveTripScreen() {
  const router = useRouter()
  const [countdown, setCountdown] = useState(1800) // 30 minutes in seconds

  // Countdown timer effect
  useEffect(() => {
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 0) {
          clearInterval(timer)
          return 0
        }
        return prev - 1
      })
    }, 1000)
    return () => clearInterval(timer)
  }, [])

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`
  }

  const handleGoPress = () => {
    Alert.alert("Trip Started", "Navigating to live tracking...")
    // router.push('/live-tracking'); // Uncomment if you have this route
  }

  const handleCallStudent = (student: any) => {
    Alert.alert("اتصال بالطالب", `هل تريد الاتصال بـ ${student.name}؟`, [
      { text: "إلغاء", style: "cancel" },
      {
        text: "اتصال",
        onPress: () => {
          Linking.openURL(`tel:${student.phone}`)
        },
      },
    ])
  }

  const handleGoToVehicleInfo = () => {
    Alert.alert("Vehicle Info", "Navigating to vehicle information screen...")
    // router.push('/driver-vehicle-info'); // Uncomment if you have this route
  }

  const renderStudent = ({ item }: { item: any }) => (
    <View style={styles.studentCard}>
      <View style={styles.studentInfo}>
        <View style={[styles.studentAvatar, { backgroundColor: item.statusColor }]}>
          <Text style={styles.studentAvatarText}>{item.avatar}</Text>
        </View>
        <View style={styles.studentDetails}>
          <Text style={styles.studentName}>{item.name}</Text>
          <Text style={styles.studentLocation}>{item.location}</Text>
          <Text style={styles.studentDistance}>المسافة: {item.distanceFromCenter} كم</Text>
        </View>
      </View>
      <View style={styles.studentStatus}>
        <View style={[styles.statusDot, { backgroundColor: item.statusColor }]} />
        <Text style={[styles.statusText, { color: item.statusColor }]}>{item.statusText}</Text>
        <Text style={styles.estimatedTime}>{item.estimatedTime}</Text>
        <TouchableOpacity style={styles.callButton} onPress={() => handleCallStudent(item)} activeOpacity={0.8}>
          <LinearGradient colors={["#4CAF50", "#388E3C"]} style={styles.callButtonGradient}>
            <Text style={styles.callButtonText}>📞</Text>
          </LinearGradient>
        </TouchableOpacity>
      </View>
    </View>
  )

  const mapRegion = {
    latitude: CENTRAL_LOCATION.latitude,
    longitude: CENTRAL_LOCATION.longitude,
    latitudeDelta: 0.02,
    longitudeDelta: 0.02,
  }

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#2563EB" />

      {/* Header with Gradient */}
      <LinearGradient colors={["#2563EB", "#1D4ED8"]} style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()} activeOpacity={0.7}>
          <Text style={styles.backButtonIcon}>←</Text>
        </TouchableOpacity>
        <View style={styles.headerCenter}>
          <Text style={styles.headerTitle}>تتبع الطلاب المباشر</Text>
          <Text style={styles.headerSubtitle}>Live Student Tracking</Text>
        </View>
        <View style={styles.referenceContainer}>
          <Text style={styles.referenceText}>المرجع: {ORIGINAL_COUNTDOWN}</Text>
        </View>
      </LinearGradient>

      {/* Student Login Info with Countdown Timer */}
      <View style={styles.topSection}>
        <View style={styles.loginCard}>
          <View style={styles.loginInfo}>
            <LinearGradient colors={["#2563EB", "#1D4ED8"]} style={styles.loginAvatar}>
              <Text style={styles.loginAvatarText}>{STUDENT_LOGIN.name.charAt(0)}</Text>
            </LinearGradient>
            <View style={styles.loginDetails}>
              <Text style={styles.loginName}>{STUDENT_LOGIN.name}</Text>
              <Text style={styles.loginId}>{STUDENT_LOGIN.studentId}</Text>
              <Text style={styles.loginUniversity}>{STUDENT_LOGIN.university}</Text>
            </View>
          </View>
          <View style={styles.timerSection}>
            <Text style={styles.timerLabel}>الوقت المتبقي</Text>
            <Text style={styles.timerValue}>{formatTime(countdown)}</Text>
          </View>
        </View>
      </View>

      {/* Live Map Section with Trip Trajectory */}
      <View style={styles.mapSection}>
        <View style={styles.mapContainer}>
          <Text style={styles.sectionTitle}>خريطة مواقع الطلاب المباشرة</Text>
          <View style={styles.mapWrapper}>
            <MapView
              style={styles.map}
              provider={PROVIDER_GOOGLE}
              initialRegion={mapRegion}
              showsUserLocation={true}
              showsMyLocationButton={false}
              showsCompass={false}
              toolbarEnabled={false}
            >
              {/* Central Location Marker (Driver) */}
              <Marker
                coordinate={CENTRAL_LOCATION}
                pinColor="#2563EB"
                title="موقع السائق"
                description="الموقع المركزي"
              />
              {/* Student Location Markers */}
              {STUDENTS.map((student) => (
                <Marker
                  key={student.id}
                  coordinate={{
                    latitude: student.latitude,
                    longitude: student.longitude,
                  }}
                  pinColor="#4CAF50" // Green for students
                  title={student.name}
                  description={`المسافة: ${student.distanceFromCenter} كم`}
                />
              ))}
              {/* Trip Route Markers */}
              {TRIP_ROUTE.map((location, index) => (
                <Marker
                  key={`route-${index}`}
                  coordinate={location}
                  pinColor={index === 0 ? "#10b981" : index === TRIP_ROUTE.length - 1 ? "#ef4444" : "#f59e0b"}
                  title={location.title}
                />
              ))}
            </MapView>
          </View>
        </View>
      </View>

      {/* Students List Section */}
      <View style={styles.studentsSection}>
        <View style={styles.studentsSectionHeader}>
          <Text style={styles.sectionTitle}>قائمة الطلاب</Text>
          <Text style={styles.studentsCount}>{STUDENTS.length} طلاب</Text>
        </View>
        <FlatList
          data={STUDENTS}
          renderItem={renderStudent}
          keyExtractor={(item) => item.id.toString()}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.studentsList}
        />
      </View>

      {/* Vehicle Info Button */}
      <View style={styles.actionSection}>
        <TouchableOpacity style={styles.vehicleInfoButton} onPress={handleGoToVehicleInfo} activeOpacity={0.8}>
          <LinearGradient colors={["#2563EB", "#1D4ED8"]} style={styles.vehicleInfoButtonGradient}>
            <Text style={styles.vehicleInfoButtonText}>معلومات المركبة</Text>
            <Text style={styles.vehicleInfoButtonIcon}>🚗</Text>
          </LinearGradient>
        </TouchableOpacity>
      </View>

      {/* GO Button */}
      <View style={styles.goButtonContainer}>
        <TouchableOpacity style={styles.goButton} onPress={handleGoPress} activeOpacity={0.8}>
          <Text style={styles.goButtonText}>GO</Text>
        </TouchableOpacity>
      </View>

      {/* Navigation Bar */}
      <DriverNavigationBar currentScreen="live-trip" />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F8FAFC", // Light background
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    paddingTop: Platform.OS === "ios" ? 50 : 30,
    paddingBottom: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    justifyContent: "center",
    alignItems: "center",
  },
  backButtonIcon: {
    fontSize: 20,
    color: "#FFFFFF",
    fontWeight: "bold",
  },
  headerCenter: {
    flex: 1,
    alignItems: "center",
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#FFFFFF",
    textAlign: "center",
  },
  headerSubtitle: {
    fontSize: 13,
    color: "rgba(255, 255, 255, 0.8)",
    textAlign: "center",
    marginTop: 2,
  },
  referenceContainer: {
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 15,
  },
  referenceText: {
    color: "#FFFFFF",
    fontSize: 13,
    fontWeight: "600",
  },
  topSection: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: "#F8FAFC",
  },
  loginCard: {
    borderRadius: 16,
    padding: 18,
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#FFFFFF", // White background for cards
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 6,
    elevation: 5,
  },
  loginInfo: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
  },
  loginAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  loginAvatarText: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#FFFFFF",
  },
  loginDetails: {
    flex: 1,
  },
  loginName: {
    fontSize: 17,
    fontWeight: "600",
    color: "#1F2937", // Dark gray
    textAlign: "right",
  },
  loginId: {
    fontSize: 13,
    color: "#4B5563", // Medium gray
    marginTop: 2,
    textAlign: "right",
  },
  loginUniversity: {
    fontSize: 11,
    color: "#6B7280", // Light gray
    marginTop: 2,
    textAlign: "right",
  },
  timerSection: {
    alignItems: "center",
    marginLeft: 10,
  },
  timerLabel: {
    fontSize: 11,
    color: "#6B7280",
    marginBottom: 4,
  },
  timerValue: {
    fontSize: 22,
    fontWeight: "bold",
    color: "#2563EB", // Primary blue
  },
  mapSection: {
    marginHorizontal: 20,
    marginBottom: 15,
  },
  mapContainer: {
    borderRadius: 16,
    padding: 18,
    backgroundColor: "#FFFFFF",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 6,
    elevation: 5,
  },
  sectionTitle: {
    fontSize: 17,
    fontWeight: "bold",
    color: "#1F2937",
    textAlign: "center",
    marginBottom: 15,
  },
  mapWrapper: {
    height: 200,
    borderRadius: 12,
    overflow: "hidden",
    borderWidth: 1,
    borderColor: "#E0E7FF", // Very light blue border
  },
  map: {
    flex: 1,
  },
  studentsSection: {
    flex: 1,
    paddingHorizontal: 20,
  },
  studentsSectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 18,
    paddingVertical: 14,
    borderRadius: 16,
    marginBottom: 12,
    backgroundColor: "#FFFFFF",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 3,
  },
  studentsCount: {
    fontSize: 15,
    fontWeight: "600",
    color: "#4B5563",
  },
  studentsList: {
    paddingBottom: 20,
  },
  studentCard: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 14,
    borderRadius: 12,
    marginBottom: 10,
    backgroundColor: "#FFFFFF",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 3,
  },
  studentInfo: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  studentAvatar: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 2,
  },
  studentAvatarText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "bold",
  },
  studentDetails: {
    flex: 1,
  },
  studentName: {
    fontSize: 16,
    fontWeight: "600",
    color: "#1F2937",
    marginBottom: 2,
  },
  studentLocation: {
    fontSize: 13,
    color: "#6B7280",
    marginTop: 2,
    textAlign: "right",
  },
  studentDistance: {
    fontSize: 13,
    color: "#4B5563",
    fontWeight: "500",
    marginTop: 2,
  },
  studentStatus: {
    alignItems: "center",
    marginLeft: 10,
  },
  statusDot: {
    width: 7,
    height: 7,
    borderRadius: 3.5,
    marginBottom: 4,
  },
  statusText: {
    fontSize: 11,
    fontWeight: "600",
    marginBottom: 2,
  },
  estimatedTime: {
    fontSize: 10,
    color: "#9CA3AF",
    marginBottom: 8,
  },
  callButton: {
    marginLeft: 8,
  },
  callButtonGradient: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  callButtonText: {
    fontSize: 18,
  },
  actionSection: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  vehicleInfoButton: {
    borderRadius: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 6,
  },
  vehicleInfoButtonGradient: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderRadius: 12,
  },
  vehicleInfoButtonText: {
    color: "#FFFFFF",
    fontSize: 17,
    fontWeight: "bold",
    marginRight: 10,
  },
  vehicleInfoButtonIcon: {
    fontSize: 18,
  },
  goButtonContainer: {
    position: "absolute",
    bottom: 100, // Adjust based on navigation bar height
    right: 25,
  },
  goButton: {
    width: 65,
    height: 65,
    borderRadius: 32.5,
    backgroundColor: "#2563EB", // Primary blue
    alignItems: "center",
    justifyContent: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.25,
    shadowRadius: 10,
    elevation: 9,
    borderWidth: 2,
    borderColor: "white",
  },
  goButtonText: {
    fontSize: 18,
    fontWeight: "800",
    color: "white",
    letterSpacing: 0.5,
  },
})
