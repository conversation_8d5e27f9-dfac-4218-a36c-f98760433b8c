import { Colors } from '@/constants/Colors';
import { useLocationTracking } from '@/hooks/useLocationTracking';
import { useRouter } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
    Dimensions,
    Platform,
    StatusBar,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import MapView, { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, PROVIDER_GOOGLE } from 'react-native-maps';

const { width, height } = Dimensions.get('window');

// Trip route data
const TRIP_ROUTE = [
  { latitude: 34.0209, longitude: -6.8416, title: 'نقطة البداية' },
  { latitude: 34.0250, longitude: -6.8350, title: 'محطة الحافلة' },
  { latitude: 34.0300, longitude: -6.8200, title: 'الجامعة' },
];

// Student info
const STUDENT_INFO = {
  name: 'أحمد محمد',
  studentId: 'ST-2024-001',
  tripId: 'TR-2024-001',
};

export default function LiveTrackingScreen() {
  const router = useRouter();
  const {
    currentLocation,
    isTracking,
    hasPermission,
    error,
    startTracking,
    stopTracking,
  } = useLocationTracking();

  const [countdown, setCountdown] = useState(2400); // 40 minutes in seconds
  const [tripStatus, setTripStatus] = useState<'active' | 'completed'>('active');

  // Countdown timer effect
  useEffect(() => {
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 0) {
          clearInterval(timer);
          setTripStatus('completed');
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Start location tracking on mount
  useEffect(() => {
    const initializeTracking = async () => {
      await startTracking();
    };
    initializeTracking();

    return () => {
      stopTracking();
    };
  }, []);

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getMapRegion = () => {
    if (currentLocation) {
      return {
        latitude: currentLocation.latitude,
        longitude: currentLocation.longitude,
        latitudeDelta: 0.02,
        longitudeDelta: 0.02,
      };
    }
    
    // Default to trip start location
    return {
      latitude: TRIP_ROUTE[0].latitude,
      longitude: TRIP_ROUTE[0].longitude,
      latitudeDelta: 0.02,
      longitudeDelta: 0.02,
    };
  };

  const handleEndTrip = () => {
    stopTracking();
    router.back();
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />

      {/* Enhanced Countdown Timer at Top */}
      <View style={styles.timerOverlay}>
        <View style={styles.timerCard}>
          <View style={styles.timerHeader}>
            <Text style={styles.timerIcon}>⏱️</Text>
            <Text style={styles.timerLabel}>الوقت المتبقي للوصول</Text>
          </View>
          <Text style={styles.timerValue}>{formatTime(countdown)}</Text>
          <View style={styles.statusIndicator}>
            <View style={[styles.statusDot, {
              backgroundColor: isTracking ? Colors.light.success : Colors.light.warning
            }]} />
            <Text style={styles.statusText}>
              {isTracking ? 'متصل' : 'غير متصل'}
            </Text>
          </View>
          <View style={[styles.studentInfo, { alignItems: 'center', marginTop: 12, paddingTop: 12, borderTopWidth: 1, borderTopColor: 'rgba(0, 0, 0, 0.1)' }]}>
            <Text style={[styles.studentName, { textAlign: 'center', fontSize: 16 }]}>{STUDENT_INFO.name}</Text>
            <Text style={[styles.studentId, { textAlign: 'center', fontSize: 12 }]}>{STUDENT_INFO.studentId}</Text>
          </View>
        </View>
      </View>

      {/* Full Screen Live Tracking Map */}
      <MapView
        style={styles.map}
        provider={PROVIDER_GOOGLE}
        region={getMapRegion()}
        showsUserLocation={true}
        showsMyLocationButton={false}
        showsCompass={true}
        mapType="standard"
        customMapStyle={[
          {
            featureType: 'poi',
            elementType: 'labels',
            stylers: [{ visibility: 'off' }],
          },
        ]}
      >
        {/* Route Polyline */}
        <Polyline
          coordinates={TRIP_ROUTE}
          strokeColor={Colors.light.primary}
          strokeWidth={4}
          lineDashPattern={[0]}
        />
        
        {/* Route Markers */}
        {TRIP_ROUTE.map((location, index) => (
          <Marker
            key={index}
            coordinate={location}
            pinColor={
              index === 0 ? Colors.light.success : 
              index === TRIP_ROUTE.length - 1 ? Colors.light.error : 
              Colors.light.warning
            }
            title={location.title}
          />
        ))}

        {/* Current Location Marker */}
        {currentLocation && (
          <Marker
            coordinate={{
              latitude: currentLocation.latitude,
              longitude: currentLocation.longitude,
            }}
            title="موقعك الحالي"
            pinColor={Colors.light.info}
          />
        )}
      </MapView>

      {/* Bottom Control Panel */}
      <View style={styles.bottomPanel}>
        <View style={styles.studentInfoCard}>
          <View style={styles.studentInfo}>
            <View style={styles.studentAvatar}>
              <Text style={styles.studentAvatarText}>{STUDENT_INFO.name.charAt(0)}</Text>
            </View>
            <View style={styles.studentDetails}>
              <Text style={styles.studentName}>{STUDENT_INFO.name}</Text>
              <Text style={styles.studentId}>{STUDENT_INFO.studentId}</Text>
              <Text style={styles.tripId}>رحلة: {STUDENT_INFO.tripId}</Text>
            </View>
          </View>
          
          <View style={styles.tripStats}>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>
                {tripStatus === 'active' ? 'نشط' : 'مكتمل'}
              </Text>
              <Text style={styles.statLabel}>حالة الرحلة</Text>
            </View>
            <View style={styles.statDivider} />
            <View style={styles.statItem}>
              <Text style={styles.statValue}>
                {currentLocation ? 'متصل' : 'غير متصل'}
              </Text>
              <Text style={styles.statLabel}>حالة الموقع</Text>
            </View>
          </View>
        </View>

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={styles.emergencyButton}
            onPress={() => {/* Handle emergency */}}
            activeOpacity={0.8}
          >
            <Text style={styles.emergencyIcon}>🚨</Text>
            <Text style={styles.emergencyText}>طوارئ</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.endTripButton}
            onPress={handleEndTrip}
            activeOpacity={0.8}
          >
            <Text style={styles.endTripButtonText}>إنهاء الرحلة</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Floating Back Button */}
      <TouchableOpacity
        style={styles.floatingBackButton}
        onPress={() => router.back()}
        activeOpacity={0.8}
      >
        <Text style={styles.backIcon}>←</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  map: {
    flex: 1,
  },
  timerOverlay: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? 70 : 50,
    left: 20,
    right: 20,
    zIndex: 1000,
  },
  timerCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.98)',
    borderRadius: 20,
    padding: 24,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.18,
    shadowRadius: 16,
    elevation: 12,
    backdropFilter: 'blur(20px)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  timerLabel: {
    fontSize: 15,
    color: Colors.light.textSecondary,
    marginBottom: 10,
    textAlign: 'center',
    fontWeight: '500',
  },
  timerValue: {
    fontSize: 36,
    fontWeight: '800',
    color: Colors.light.primary,
    marginBottom: 14,
    letterSpacing: 2,
    fontVariant: ['tabular-nums'],
  },
  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  statusText: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    fontWeight: '500',
  },
  floatingBackButton: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? 60 : 40,
    left: 20,
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    zIndex: 999,
  },
  backIcon: {
    fontSize: 20,
    color: Colors.light.primary,
    fontWeight: '600',
  },
  bottomPanel: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.98)',
    borderTopLeftRadius: 28,
    borderTopRightRadius: 28,
    padding: 28,
    paddingBottom: Platform.OS === 'ios' ? 44 : 28,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -6 },
    shadowOpacity: 0.18,
    shadowRadius: 16,
    elevation: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.05)',
  },
  studentInfoCard: {
    marginBottom: 20,
  },
  studentInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  studentAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: Colors.light.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  studentAvatarText: {
    fontSize: 20,
    fontWeight: '700',
    color: 'white',
  },
  studentDetails: {
    flex: 1,
  },
  studentName: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.text,
    textAlign: 'right',
  },
  studentId: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    marginTop: 2,
    textAlign: 'right',
  },
  tripId: {
    fontSize: 12,
    color: Colors.light.textTertiary,
    marginTop: 2,
    textAlign: 'right',
  },
  tripStats: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f8fafc',
    borderRadius: 12,
    padding: 16,
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statValue: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: Colors.light.textSecondary,
  },
  statDivider: {
    width: 1,
    height: 30,
    backgroundColor: Colors.light.border,
    marginHorizontal: 16,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  emergencyButton: {
    flex: 1,
    backgroundColor: Colors.light.error,
    borderRadius: 16,
    paddingVertical: 18,
    alignItems: 'center',
    shadowColor: Colors.light.error,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 6,
  },
  emergencyIcon: {
    fontSize: 22,
    marginBottom: 6,
  },
  emergencyText: {
    fontSize: 15,
    fontWeight: '700',
    color: 'white',
  },
  endTripButton: {
    flex: 2,
    backgroundColor: Colors.light.primary,
    borderRadius: 16,
    paddingVertical: 18,
    alignItems: 'center',
    shadowColor: Colors.light.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 6,
  },
  endTripButtonText: {
    fontSize: 17,
    fontWeight: '700',
    color: 'white',
  },
  // New enhanced timer styles
  timerHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  timerIcon: {
    fontSize: 20,
    marginRight: 8,
  },
});
