import { useLocationTracking } from '@/hooks/useLocationTracking';
import { useRouter } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
    Dimensions,
    FlatList,
    Platform,
    StatusBar,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';

// Conditional import for maps (only on native platforms)
let MapView: any, Marker: any, Polyline: any, PROVIDER_GOOGLE: any;
if (Platform.OS !== 'web') {
  const maps = require('react-native-maps');
  MapView = maps.default;
  Marker = maps.Marker;
  Polyline = maps.Polyline;
  PROVIDER_GOOGLE = maps.PROVIDER_GOOGLE;
}

const { width, height } = Dimensions.get('window');

// Trip route data
const TRIP_ROUTE = [
  { latitude: 34.0209, longitude: -6.8416, title: 'نقطة البداية' },
  { latitude: 34.0250, longitude: -6.8350, title: 'محطة الحافلة' },
  { latitude: 34.0300, longitude: -6.8200, title: 'الجامعة' },
];

// Student info
const STUDENT_INFO = {
  name: 'أحمد محمد',
  studentId: 'ST-2024-001',
  tripId: 'TR-2024-001',
};

// Students list for the bottom section
const STUDENTS_LIST = [
  {
    id: 1,
    name: 'AHMAD',
    avatar: 'A',
    status: 'picked',
    statusColor: '#10b981',
  },
  {
    id: 2,
    name: 'ALI',
    avatar: 'A',
    status: 'picked',
    statusColor: '#10b981',
  },
  {
    id: 3,
    name: 'AMIN',
    avatar: 'A',
    status: 'picked',
    statusColor: '#10b981',
  },
  {
    id: 4,
    name: 'SARA',
    avatar: 'S',
    status: 'waiting',
    statusColor: '#f59e0b',
  },
];

export default function LiveTrackingScreen() {
  const router = useRouter();
  const {
    currentLocation,
    isTracking,
    hasPermission,
    error,
    startTracking,
    stopTracking,
  } = useLocationTracking();

  const [countdown, setCountdown] = useState(2400); // 40 minutes in seconds
  const [tripStatus, setTripStatus] = useState<'active' | 'completed'>('active');

  // Countdown timer effect
  useEffect(() => {
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 0) {
          clearInterval(timer);
          setTripStatus('completed');
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Start location tracking on mount
  useEffect(() => {
    const initializeTracking = async () => {
      await startTracking();
    };
    initializeTracking();

    return () => {
      stopTracking();
    };
  }, []);

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getMapRegion = () => {
    if (currentLocation) {
      return {
        latitude: currentLocation.latitude,
        longitude: currentLocation.longitude,
        latitudeDelta: 0.02,
        longitudeDelta: 0.02,
      };
    }
    
    // Default to trip start location
    return {
      latitude: TRIP_ROUTE[0].latitude,
      longitude: TRIP_ROUTE[0].longitude,
      latitudeDelta: 0.02,
      longitudeDelta: 0.02,
    };
  };

  const handleEndTrip = () => {
    stopTracking();
    router.back();
  };

  const renderStudent = ({ item }: { item: any }) => (
    <View style={styles.studentItem}>
      <View style={styles.studentLeft}>
        <View style={[styles.studentAvatar, { backgroundColor: item.statusColor }]}>
          <Text style={styles.studentAvatarText}>{item.avatar}</Text>
        </View>
        <Text style={styles.studentName}>{item.name}</Text>
      </View>
      <View style={[styles.statusIndicator, { backgroundColor: item.statusColor }]}>
        <Text style={styles.statusIcon}>✓</Text>
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />

      {/* Full Screen Map */}
      <View style={styles.mapContainer}>
        {Platform.OS === 'web' ? (
          // Web placeholder
          <View style={[styles.map, styles.webMapPlaceholder]}>
            <Text style={styles.webMapText}>🗺️</Text>
            <Text style={styles.webMapSubtext}>Live Map View</Text>
            <Text style={styles.webMapNote}>(Maps work on mobile devices)</Text>
          </View>
        ) : (
          // Native map
          <MapView
            style={styles.map}
            provider={PROVIDER_GOOGLE}
            region={getMapRegion()}
            showsUserLocation={true}
            showsMyLocationButton={false}
            showsCompass={false}
            mapType="standard"
          >
            {/* Route Polyline */}
            <Polyline
              coordinates={TRIP_ROUTE}
              strokeColor="#3b82f6"
              strokeWidth={4}
              lineDashPattern={[5, 5]}
            />

            {/* Bus Marker */}
            <Marker
              coordinate={currentLocation || TRIP_ROUTE[0]}
              title="الحافلة"
              description="موقع الحافلة الحالي"
            >
              <View style={styles.busMarker}>
                <Text style={styles.busIcon}>🚌</Text>
              </View>
            </Marker>

            {/* Destination Marker */}
            <Marker
              coordinate={TRIP_ROUTE[TRIP_ROUTE.length - 1]}
              title="المدرسة"
              description="وجهة الرحلة"
            >
              <View style={styles.destinationMarker}>
                <Text style={styles.destinationIcon}>🏫</Text>
              </View>
            </Marker>
          </MapView>
        )}

        {/* Circular Timer Overlay */}
        <View style={styles.timerOverlay}>
          <View style={styles.circularTimer}>
            <Text style={styles.timerText}>{formatTime(countdown)}</Text>
            <Text style={styles.timerLabel}>50KM</Text>
          </View>
        </View>
      </View>

      {/* Bottom Students List */}
      <View style={styles.bottomSheet}>
        <View style={styles.bottomSheetHeader}>
          <View style={styles.dragHandle} />
          <Text style={styles.bottomSheetTitle}>الطلاب المتبقون</Text>
        </View>

        <FlatList
          data={STUDENTS_LIST}
          renderItem={renderStudent}
          keyExtractor={(item) => item.id.toString()}
          style={styles.studentsList}
          showsVerticalScrollIndicator={false}
        />
      </View>

      {/* Back Button */}
      <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
        <Text style={styles.backIcon}>←</Text>
      </TouchableOpacity>
    </View>
  );
};


const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  mapContainer: {
    flex: 1,
  },
  map: {
    flex: 1,
  },
  // Circular Timer Overlay
  timerOverlay: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? 80 : 60,
    right: 20,
    zIndex: 1000,
  },
  circularTimer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
    borderWidth: 2,
    borderColor: '#e5e7eb',
  },
  timerText: {
    fontSize: 14,
    fontWeight: '700',
    color: '#1f2937',
    textAlign: 'center',
  },
  timerLabel: {
    fontSize: 10,
    color: '#6b7280',
    fontWeight: '500',
    marginTop: 2,
  },
  // Map Markers
  busMarker: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#3b82f6',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  busIcon: {
    fontSize: 20,
  },
  destinationMarker: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#10b981',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  destinationIcon: {
    fontSize: 20,
  },
  // Bottom Sheet for Students List
  bottomSheet: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: '#ffffff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 12,
    paddingBottom: Platform.OS === 'ios' ? 34 : 20,
    maxHeight: '40%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 12,
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
  },
  bottomSheetHeader: {
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  dragHandle: {
    width: 40,
    height: 4,
    backgroundColor: '#d1d5db',
    borderRadius: 2,
    marginBottom: 12,
  },
  bottomSheetTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    textAlign: 'center',
  },
  studentsList: {
    paddingHorizontal: 20,
  },
  // Student Item Styles
  studentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginVertical: 4,
    backgroundColor: '#f9fafb',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  studentLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  studentAvatar: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  studentAvatarText: {
    fontSize: 14,
    fontWeight: '700',
    color: '#ffffff',
  },
  studentName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
  },
  statusIndicator: {
    width: 24,
    height: 24,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  statusIcon: {
    fontSize: 12,
    color: '#ffffff',
    fontWeight: '700',
  },
  // Back Button
  backButton: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? 60 : 40,
    left: 20,
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    zIndex: 999,
  },
  backIcon: {
    fontSize: 20,
    color: '#3b82f6',
    fontWeight: '600',
  },
  // Web Map Placeholder Styles
  webMapPlaceholder: {
    backgroundColor: '#f3f4f6',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#e5e7eb',
    borderStyle: 'dashed',
  },
  webMapText: {
    fontSize: 48,
    marginBottom: 12,
  },
  webMapSubtext: {
    fontSize: 18,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 8,
  },
  webMapNote: {
    fontSize: 14,
    color: '#6b7280',
    textAlign: 'center',
  },
});
